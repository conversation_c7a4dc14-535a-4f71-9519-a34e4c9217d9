#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序
用于测试阿奇索闲鱼自动发货工具的基本功能
"""

import sys
import os

# 添加功能模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '功能模块'))

def test_config_manager():
    """测试配置管理器"""
    print("测试配置管理器...")
    
    from 配置管理 import ConfigManager
    
    config_manager = ConfigManager()
    
    # 测试默认配置
    config = config_manager.load_config()
    print(f"默认配置: {config}")
    
    # 测试更新认证信息
    test_auth = "Bearer test_token"
    test_cookie = "session=test_session; user=test_user"
    
    result = config_manager.update_auth_info(test_auth, test_cookie)
    print(f"更新认证信息结果: {result}")
    
    # 测试加载更新后的配置
    updated_config = config_manager.load_config()
    print(f"更新后配置: {updated_config}")
    
    # 测试获取认证头
    headers = config_manager.get_auth_headers()
    print(f"认证头: {headers}")
    
    print("配置管理器测试完成\n")


def test_api_validator():
    """测试API验证器"""
    print("测试API验证器...")
    
    from API验证 import SyncApiValidator
    
    # 使用测试数据
    test_auth = "Bearer test_token"
    test_cookie = "session=test_session"
    
    # 测试同步验证（预期会失败，因为是测试数据）
    is_valid, result = SyncApiValidator.validate_auth(test_auth, test_cookie)
    print(f"验证结果: {is_valid}")
    print(f"返回信息: {result}")
    
    print("API验证器测试完成\n")


def test_clipboard_parsing():
    """测试剪贴板解析逻辑"""
    print("测试剪贴板解析逻辑...")
    
    # 模拟剪贴板内容
    test_cases = [
        "Bearer token123====session=abc123; user=test",
        "invalid_format",
        "====",
        "auth====",
        "====cookie",
        ""
    ]
    
    for test_case in test_cases:
        print(f"测试内容: '{test_case}'")
        
        if "====" not in test_case:
            print("  结果: 格式错误")
            continue
        
        parts = test_case.split("====", 1)
        if len(parts) != 2:
            print("  结果: 分割错误")
            continue
        
        authorization = parts[0].strip()
        cookie = parts[1].strip()
        
        if not authorization or not cookie:
            print("  结果: 内容为空")
            continue
        
        print(f"  Authorization: {authorization}")
        print(f"  Cookie: {cookie}")
        print("  结果: 解析成功")
    
    print("剪贴板解析测试完成\n")


def main():
    """主测试函数"""
    print("=" * 50)
    print("阿奇索闲鱼自动发货工具 - 功能测试")
    print("=" * 50)
    
    try:
        test_config_manager()
        test_api_validator()
        test_clipboard_parsing()
        
        print("所有测试完成！")
        print("\n如果没有错误信息，说明基础功能正常。")
        print("现在可以运行主程序.py来启动图形界面。")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖已安装：pip install PyQt5 requests")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")


if __name__ == "__main__":
    main()
