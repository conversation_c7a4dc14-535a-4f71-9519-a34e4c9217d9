#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责配置文件的读取、写入和管理
"""

import json
import os
from typing import Dict, Optional


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "配置文件"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "用户配置.json")
        self._ensure_config_dir()
        
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def load_config(self) -> Dict:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if not os.path.exists(self.config_file):
            return self._get_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 确保配置包含所有必要字段
                default_config = self._get_default_config()
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except (json.JSONDecodeError, FileNotFoundError):
            return self._get_default_config()
    
    def save_config(self, config: Dict) -> bool:
        """
        保存配置文件
        
        Args:
            config: 配置字典
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _get_default_config(self) -> Dict:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            "authorization": "",
            "cookie": "",
            "user_info": {},
            "last_validation_time": "",
            "validation_status": False,
            "auto_validate": True,
            "theme": "light"
        }
    
    def update_auth_info(self, authorization: str, cookie: str) -> bool:
        """
        更新认证信息
        
        Args:
            authorization: Authorization头
            cookie: Cookie信息
            
        Returns:
            是否更新成功
        """
        config = self.load_config()
        config["authorization"] = authorization
        config["cookie"] = cookie
        config["validation_status"] = False  # 重置验证状态
        config["user_info"] = {}  # 清空用户信息
        return self.save_config(config)
    
    def update_validation_result(self, is_valid: bool, user_info: Dict = None) -> bool:
        """
        更新验证结果
        
        Args:
            is_valid: 是否验证成功
            user_info: 用户信息
            
        Returns:
            是否更新成功
        """
        config = self.load_config()
        config["validation_status"] = is_valid
        config["user_info"] = user_info or {}
        
        # 更新验证时间
        from datetime import datetime
        config["last_validation_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return self.save_config(config)
    
    def get_auth_headers(self) -> Dict[str, str]:
        """
        获取认证头信息
        
        Returns:
            认证头字典
        """
        config = self.load_config()
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        
        if config.get("authorization"):
            headers["Authorization"] = config["authorization"]
        
        if config.get("cookie"):
            headers["Cookie"] = config["cookie"]
            
        return headers
    
    def is_auth_configured(self) -> bool:
        """
        检查是否已配置认证信息
        
        Returns:
            是否已配置
        """
        config = self.load_config()
        return bool(config.get("authorization") and config.get("cookie"))
    
    def get_user_info(self) -> Dict:
        """
        获取用户信息
        
        Returns:
            用户信息字典
        """
        config = self.load_config()
        return config.get("user_info", {})
