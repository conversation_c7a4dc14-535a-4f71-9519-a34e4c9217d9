#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
阿奇索闲鱼自动发货工具的主界面
"""

import sys
import re
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QTextEdit, QPushButton, QFrame, QMessageBox,
    QProgressBar, QGroupBox, QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPixmap, QClipboard
from PyQt5.QtWidgets import QApplication

from 配置管理 import ConfigManager
from API验证 import ApiValidator


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.api_validator = None
        self.init_ui()
        self.load_saved_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("阿奇索闲鱼自动发货工具 v1.0")
        self.setGeometry(100, 100, 1000, 700)
        self.setMinimumSize(800, 600)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（水平布局）
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建左侧功能区域
        self.create_left_panel(main_layout)

        # 创建右侧日志区域
        self.create_right_panel(main_layout)

        # 应用样式
        self.apply_styles()
        
    def create_left_panel(self, parent_layout):
        """创建左侧功能面板"""
        left_widget = QWidget()
        left_widget.setObjectName("leftPanel")
        left_widget.setFixedWidth(350)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 创建认证区域
        self.create_auth_section(left_layout)

        # 创建商品发布区域
        self.create_product_section(left_layout)

        # 添加弹性空间
        left_layout.addStretch()

        parent_layout.addWidget(left_widget)
        
    def create_auth_section(self, parent_layout):
        """创建认证配置区域"""
        auth_group = QGroupBox("账号认证")
        auth_group.setObjectName("authGroup")
        auth_group.setMaximumHeight(120)
        auth_layout = QVBoxLayout(auth_group)

        # 用户信息显示
        self.user_info_layout = QHBoxLayout()
        self.user_nickname_label = QLabel("未登录")
        self.user_nickname_label.setObjectName("userNickname")

        # 按钮区域
        button_layout = QHBoxLayout()

        # 粘贴并解析按钮
        self.paste_button = QPushButton("粘贴认证")
        self.paste_button.setObjectName("pasteButton")
        self.paste_button.clicked.connect(self.paste_and_parse)

        # 验证按钮
        self.validate_button = QPushButton("验证")
        self.validate_button.setObjectName("validateButton")
        self.validate_button.clicked.connect(self.validate_auth)
        self.validate_button.setEnabled(False)

        button_layout.addWidget(self.paste_button)
        button_layout.addWidget(self.validate_button)

        # 用户信息行
        user_info_row = QHBoxLayout()
        user_info_row.addWidget(QLabel("用户:"))
        user_info_row.addWidget(self.user_nickname_label)
        user_info_row.addStretch()

        auth_layout.addLayout(user_info_row)
        auth_layout.addLayout(button_layout)

        parent_layout.addWidget(auth_group)
        
    def create_product_section(self, parent_layout):
        """创建商品发布区域"""
        product_group = QGroupBox("商品发布")
        product_group.setObjectName("productGroup")
        product_layout = QVBoxLayout(product_group)

        # 发布按钮区域
        publish_layout = QGridLayout()

        # 单个发布按钮
        self.single_publish_button = QPushButton("发布单个商品")
        self.single_publish_button.setObjectName("singlePublishButton")
        self.single_publish_button.clicked.connect(self.single_publish_placeholder)

        # 批量发布按钮
        self.batch_publish_button = QPushButton("批量发布商品")
        self.batch_publish_button.setObjectName("batchPublishButton")
        self.batch_publish_button.clicked.connect(self.batch_publish_placeholder)

        # 商品管理按钮
        self.manage_products_button = QPushButton("商品管理")
        self.manage_products_button.setObjectName("manageProductsButton")
        self.manage_products_button.clicked.connect(self.manage_products_placeholder)

        # 设置按钮
        self.settings_button = QPushButton("发布设置")
        self.settings_button.setObjectName("settingsButton")
        self.settings_button.clicked.connect(self.settings_placeholder)

        publish_layout.addWidget(self.single_publish_button, 0, 0)
        publish_layout.addWidget(self.batch_publish_button, 0, 1)
        publish_layout.addWidget(self.manage_products_button, 1, 0)
        publish_layout.addWidget(self.settings_button, 1, 1)

        product_layout.addLayout(publish_layout)

        parent_layout.addWidget(product_group)
        
    def create_right_panel(self, parent_layout):
        """创建右侧日志面板"""
        right_widget = QWidget()
        right_widget.setObjectName("rightPanel")
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 日志区域
        log_group = QGroupBox("操作日志")
        log_group.setObjectName("logGroup")
        log_layout = QVBoxLayout(log_group)

        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setObjectName("logDisplay")
        self.log_display.setReadOnly(True)
        self.log_display.setPlaceholderText("操作日志将在此显示...")

        # 日志控制按钮
        log_control_layout = QHBoxLayout()

        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.setObjectName("clearLogButton")
        self.clear_log_button.clicked.connect(self.clear_log)

        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.setObjectName("saveLogButton")
        self.save_log_button.clicked.connect(self.save_log_placeholder)

        log_control_layout.addWidget(self.clear_log_button)
        log_control_layout.addWidget(self.save_log_button)
        log_control_layout.addStretch()

        log_layout.addWidget(self.log_display)
        log_layout.addLayout(log_control_layout)

        # 状态区域
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QVBoxLayout(status_frame)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)

        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.status_label)

        right_layout.addWidget(log_group)
        right_layout.addWidget(status_frame)

        parent_layout.addWidget(right_widget)
        
    def apply_styles(self):
        """应用样式表"""
        style = """
        * {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
        }

        QMainWindow {
            background-color: #f8f9fa;
        }

        #leftPanel {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-right: 5px;
        }

        #rightPanel {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-left: 5px;
        }

        #statusFrame {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 13px;
            color: #2c3e50;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-top: 8px;
            padding-top: 8px;
            background-color: #fafbfc;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 6px 0 6px;
            background-color: #fafbfc;
        }

        #userNickname {
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            font-weight: 500;
            font-size: 12px;
            min-height: 20px;
        }

        QPushButton:hover {
            background-color: #0056b3;
        }

        QPushButton:pressed {
            background-color: #004085;
        }

        QPushButton:disabled {
            background-color: #6c757d;
            color: #adb5bd;
        }

        #pasteButton {
            background-color: #28a745;
        }

        #pasteButton:hover {
            background-color: #1e7e34;
        }

        #validateButton {
            background-color: #17a2b8;
        }

        #validateButton:hover {
            background-color: #117a8b;
        }

        #singlePublishButton, #batchPublishButton {
            background-color: #fd7e14;
            padding: 12px 16px;
            font-size: 13px;
        }

        #singlePublishButton:hover, #batchPublishButton:hover {
            background-color: #e8590c;
        }

        #manageProductsButton, #settingsButton {
            background-color: #6f42c1;
        }

        #manageProductsButton:hover, #settingsButton:hover {
            background-color: #5a32a3;
        }

        #clearLogButton {
            background-color: #dc3545;
        }

        #clearLogButton:hover {
            background-color: #c82333;
        }

        #saveLogButton {
            background-color: #6c757d;
        }

        #saveLogButton:hover {
            background-color: #545b62;
        }
        
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
            background-color: #ffffff;
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            font-size: 11px;
        }

        #logDisplay {
            background-color: #f8f9fa;
            color: #495057;
            line-height: 1.4;
        }

        #statusLabel {
            font-size: 12px;
            color: #6c757d;
            padding: 5px;
            background-color: transparent;
        }

        QProgressBar {
            border: 1px solid #ced4da;
            border-radius: 5px;
            text-align: center;
            background-color: #e9ecef;
            height: 20px;
        }

        QProgressBar::chunk {
            background-color: #007bff;
            border-radius: 4px;
        }
        """
        self.setStyleSheet(style)

        # 初始化日志
        self.add_log("程序启动完成")

    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_display.append(log_message)

        # 自动滚动到底部
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志"""
        self.log_display.clear()
        self.add_log("日志已清空")

    def save_log_placeholder(self):
        """保存日志占位功能"""
        self.add_log("保存日志功能待开发")
        self.show_message("提示", "保存日志功能待开发", QMessageBox.Information)

    def single_publish_placeholder(self):
        """单个发布占位功能"""
        self.add_log("点击了发布单个商品按钮")
        self.show_message("提示", "发布单个商品功能待开发", QMessageBox.Information)

    def batch_publish_placeholder(self):
        """批量发布占位功能"""
        self.add_log("点击了批量发布商品按钮")
        self.show_message("提示", "批量发布商品功能待开发", QMessageBox.Information)

    def manage_products_placeholder(self):
        """商品管理占位功能"""
        self.add_log("点击了商品管理按钮")
        self.show_message("提示", "商品管理功能待开发", QMessageBox.Information)

    def settings_placeholder(self):
        """设置占位功能"""
        self.add_log("点击了发布设置按钮")
        self.show_message("提示", "发布设置功能待开发", QMessageBox.Information)

    def paste_and_parse(self):
        """粘贴并解析剪贴板内容"""
        try:
            clipboard = QApplication.clipboard()
            text = clipboard.text().strip()

            if not text:
                self.show_message("错误", "剪贴板为空，请先复制认证信息", QMessageBox.Warning)
                return

            # 解析格式：Authorization====Cookie
            if "====" not in text:
                self.show_message("错误", "格式错误！请确保格式为：Authorization====Cookie", QMessageBox.Warning)
                return

            parts = text.split("====", 1)
            if len(parts) != 2:
                self.show_message("错误", "格式错误！请确保格式为：Authorization====Cookie", QMessageBox.Warning)
                return

            authorization = parts[0].strip()
            cookie = parts[1].strip()

            if not authorization or not cookie:
                self.show_message("错误", "Authorization或Cookie不能为空", QMessageBox.Warning)
                return

            # 保存到配置
            if self.config_manager.update_auth_info(authorization, cookie):
                self.validate_button.setEnabled(True)
                self.status_label.setText("认证信息已解析并保存")
                self.add_log("认证信息解析成功")
                self.show_message("成功", "认证信息解析成功！", QMessageBox.Information)
            else:
                self.add_log("保存配置失败")
                self.show_message("错误", "保存配置失败", QMessageBox.Critical)

        except Exception as e:
            self.add_log(f"解析失败：{str(e)}")
            self.show_message("错误", f"解析失败：{str(e)}", QMessageBox.Critical)

    def validate_auth(self):
        """验证认证信息"""
        config = self.config_manager.load_config()
        authorization = config.get("认证令牌(authorization)", "")
        cookie = config.get("会话信息(cookie)", "")

        if not authorization or not cookie:
            self.show_message("错误", "请先粘贴并解析认证信息", QMessageBox.Warning)
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在验证认证信息...")
        self.add_log("开始验证认证信息...")

        # 禁用按钮
        self.validate_button.setEnabled(False)
        self.paste_button.setEnabled(False)

        # 创建验证器并开始验证
        self.api_validator = ApiValidator(authorization, cookie)
        self.api_validator.validation_finished.connect(self.on_validation_finished)
        self.api_validator.validation_error.connect(self.on_validation_error)
        self.api_validator.start()

    @pyqtSlot(bool, dict)
    def on_validation_finished(self, is_valid: bool, user_info: dict):
        """验证完成回调"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.validate_button.setEnabled(True)
        self.paste_button.setEnabled(True)

        if is_valid:
            # 验证成功
            self.config_manager.update_validation_result(True, user_info)
            self.status_label.setText("✓ 认证信息验证成功")
            self.update_user_display(user_info)
            self.add_log(f"认证验证成功，用户：{user_info.get('nickname', '未知')}")
            self.show_message("成功", "认证信息验证成功！", QMessageBox.Information)
        else:
            # 验证失败
            self.config_manager.update_validation_result(False)
            error_msg = user_info.get("error", "未知错误")
            self.status_label.setText(f"✗ 验证失败：{error_msg}")
            self.update_user_display(None)
            self.add_log(f"认证验证失败：{error_msg}")
            self.show_message("验证失败", f"认证信息无效：{error_msg}", QMessageBox.Warning)

    @pyqtSlot(str)
    def on_validation_error(self, error_message: str):
        """验证错误回调"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.validate_button.setEnabled(True)
        self.paste_button.setEnabled(True)

        self.status_label.setText(f"✗ 验证出错：{error_message}")
        self.update_user_display(None)
        self.add_log(f"验证出错：{error_message}")
        self.show_message("验证出错", f"验证过程中出现错误：{error_message}", QMessageBox.Critical)

    def update_user_display(self, user_info: dict):
        """更新用户显示"""
        if user_info:
            nickname = user_info.get("nickname", "未知用户")
            self.user_nickname_label.setText(nickname)
            self.user_nickname_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.user_nickname_label.setText("未登录")
            self.user_nickname_label.setStyleSheet("color: #6c757d;")

    def load_saved_config(self):
        """加载已保存的配置"""
        config = self.config_manager.load_config()

        if config.get("认证令牌(authorization)") and config.get("会话信息(cookie)"):
            self.validate_button.setEnabled(True)

            if config.get("验证状态(validation_status)") and config.get("用户信息(user_info)"):
                self.update_user_display(config["用户信息(user_info)"])
                self.status_label.setText("✓ 已加载保存的认证信息")
                self.add_log("已加载保存的认证信息")
            else:
                self.status_label.setText("已加载认证信息，建议重新验证")
                self.add_log("已加载认证信息，建议重新验证")

    def show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """显示消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.exec_()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.api_validator and self.api_validator.isRunning():
            self.api_validator.terminate()
            self.api_validator.wait()
        event.accept()
