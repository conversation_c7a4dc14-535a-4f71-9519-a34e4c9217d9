#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面模块
阿奇索闲鱼自动发货工具的主界面
"""

import sys
import re
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QTextEdit, QPushButton, QFrame, QMessageBox,
    QProgressBar, QGroupBox, QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPixmap, QClipboard
from PyQt5.QtWidgets import QApplication

from 配置管理 import ConfigManager
from API验证 import ApiValidator


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.api_validator = None
        self.init_ui()
        self.load_saved_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("阿奇索闲鱼自动发货工具 v1.0")
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(600, 500)
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.ico"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建标题
        self.create_title_section(main_layout)
        
        # 创建认证配置区域
        self.create_auth_section(main_layout)
        
        # 创建用户信息区域
        self.create_user_info_section(main_layout)
        
        # 创建状态栏
        self.create_status_section(main_layout)
        
        # 应用样式
        self.apply_styles()
        
    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_layout = QVBoxLayout(title_frame)
        
        # 主标题
        title_label = QLabel("阿奇索闲鱼自动发货工具")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 副标题
        subtitle_label = QLabel("一键配置认证信息，开启自动化之旅")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        parent_layout.addWidget(title_frame)
        
    def create_auth_section(self, parent_layout):
        """创建认证配置区域"""
        auth_group = QGroupBox("认证配置")
        auth_group.setObjectName("authGroup")
        auth_layout = QVBoxLayout(auth_group)
        
        # 说明文字
        info_label = QLabel("请复制格式为 'Authorization====Cookie' 的内容到剪贴板，然后点击下方按钮")
        info_label.setObjectName("infoLabel")
        info_label.setWordWrap(True)
        auth_layout.addWidget(info_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 粘贴并解析按钮
        self.paste_button = QPushButton("📋 一键粘贴并解析")
        self.paste_button.setObjectName("pasteButton")
        self.paste_button.clicked.connect(self.paste_and_parse)
        
        # 验证按钮
        self.validate_button = QPushButton("✓ 验证有效性")
        self.validate_button.setObjectName("validateButton")
        self.validate_button.clicked.connect(self.validate_auth)
        self.validate_button.setEnabled(False)
        
        button_layout.addWidget(self.paste_button)
        button_layout.addWidget(self.validate_button)
        button_layout.addStretch()
        
        auth_layout.addLayout(button_layout)
        
        # 认证信息显示
        self.auth_display = QTextEdit()
        self.auth_display.setObjectName("authDisplay")
        self.auth_display.setMaximumHeight(100)
        self.auth_display.setPlaceholderText("认证信息将在此显示...")
        self.auth_display.setReadOnly(True)
        auth_layout.addWidget(self.auth_display)
        
        parent_layout.addWidget(auth_group)
        
    def create_user_info_section(self, parent_layout):
        """创建用户信息区域"""
        self.user_info_group = QGroupBox("用户信息")
        self.user_info_group.setObjectName("userInfoGroup")
        self.user_info_group.setVisible(False)
        
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.user_info_layout = QGridLayout(scroll_widget)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(200)
        
        user_info_main_layout = QVBoxLayout(self.user_info_group)
        user_info_main_layout.addWidget(scroll_area)
        
        parent_layout.addWidget(self.user_info_group)
        
    def create_status_section(self, parent_layout):
        """创建状态区域"""
        status_frame = QFrame()
        status_layout = QVBoxLayout(status_frame)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.status_label)
        
        parent_layout.addWidget(status_frame)
        parent_layout.addStretch()
        
    def apply_styles(self):
        """应用样式表"""
        style = """
        QMainWindow {
            background-color: #f8f9fa;
        }
        
        #titleFrame {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 10px;
        }
        
        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        #subtitleLabel {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            color: #2c3e50;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: white;
        }
        
        #infoLabel {
            color: #6c757d;
            font-size: 12px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 13px;
        }
        
        QPushButton:hover {
            background-color: #0056b3;
        }
        
        QPushButton:pressed {
            background-color: #004085;
        }
        
        QPushButton:disabled {
            background-color: #6c757d;
            color: #adb5bd;
        }
        
        #pasteButton {
            background-color: #28a745;
        }
        
        #pasteButton:hover {
            background-color: #1e7e34;
        }
        
        #validateButton {
            background-color: #17a2b8;
        }
        
        #validateButton:hover {
            background-color: #117a8b;
        }
        
        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
            background-color: #f8f9fa;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
        }
        
        #statusLabel {
            font-size: 12px;
            color: #6c757d;
            padding: 5px;
        }
        
        QProgressBar {
            border: 1px solid #ced4da;
            border-radius: 5px;
            text-align: center;
            background-color: #e9ecef;
        }
        
        QProgressBar::chunk {
            background-color: #007bff;
            border-radius: 4px;
        }
        """
        self.setStyleSheet(style)

    def paste_and_parse(self):
        """粘贴并解析剪贴板内容"""
        try:
            clipboard = QApplication.clipboard()
            text = clipboard.text().strip()

            if not text:
                self.show_message("错误", "剪贴板为空，请先复制认证信息", QMessageBox.Warning)
                return

            # 解析格式：Authorization====Cookie
            if "====" not in text:
                self.show_message("错误", "格式错误！请确保格式为：Authorization====Cookie", QMessageBox.Warning)
                return

            parts = text.split("====", 1)
            if len(parts) != 2:
                self.show_message("错误", "格式错误！请确保格式为：Authorization====Cookie", QMessageBox.Warning)
                return

            authorization = parts[0].strip()
            cookie = parts[1].strip()

            if not authorization or not cookie:
                self.show_message("错误", "Authorization或Cookie不能为空", QMessageBox.Warning)
                return

            # 保存到配置
            if self.config_manager.update_auth_info(authorization, cookie):
                self.update_auth_display(authorization, cookie)
                self.validate_button.setEnabled(True)
                self.status_label.setText("认证信息已解析并保存")
                self.show_message("成功", "认证信息解析成功！", QMessageBox.Information)
            else:
                self.show_message("错误", "保存配置失败", QMessageBox.Critical)

        except Exception as e:
            self.show_message("错误", f"解析失败：{str(e)}", QMessageBox.Critical)

    def update_auth_display(self, authorization: str, cookie: str):
        """更新认证信息显示"""
        # 隐藏敏感信息，只显示前后几位
        auth_display = f"Authorization: {authorization[:20]}...{authorization[-10:]}" if len(authorization) > 30 else f"Authorization: {authorization}"
        cookie_display = f"Cookie: {cookie[:30]}...{cookie[-15:]}" if len(cookie) > 45 else f"Cookie: {cookie}"

        display_text = f"{auth_display}\n{cookie_display}"
        self.auth_display.setText(display_text)

    def validate_auth(self):
        """验证认证信息"""
        config = self.config_manager.load_config()
        authorization = config.get("authorization", "")
        cookie = config.get("cookie", "")

        if not authorization or not cookie:
            self.show_message("错误", "请先粘贴并解析认证信息", QMessageBox.Warning)
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在验证认证信息...")

        # 禁用按钮
        self.validate_button.setEnabled(False)
        self.paste_button.setEnabled(False)

        # 创建验证器并开始验证
        self.api_validator = ApiValidator(authorization, cookie)
        self.api_validator.validation_finished.connect(self.on_validation_finished)
        self.api_validator.validation_error.connect(self.on_validation_error)
        self.api_validator.start()

    @pyqtSlot(bool, dict)
    def on_validation_finished(self, is_valid: bool, user_info: dict):
        """验证完成回调"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.validate_button.setEnabled(True)
        self.paste_button.setEnabled(True)

        if is_valid:
            # 验证成功
            self.config_manager.update_validation_result(True, user_info)
            self.status_label.setText("✓ 认证信息验证成功")
            self.show_user_info(user_info)
            self.show_message("成功", "认证信息验证成功！", QMessageBox.Information)
        else:
            # 验证失败
            self.config_manager.update_validation_result(False)
            error_msg = user_info.get("error", "未知错误")
            self.status_label.setText(f"✗ 验证失败：{error_msg}")
            self.hide_user_info()
            self.show_message("验证失败", f"认证信息无效：{error_msg}", QMessageBox.Warning)

    @pyqtSlot(str)
    def on_validation_error(self, error_message: str):
        """验证错误回调"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮
        self.validate_button.setEnabled(True)
        self.paste_button.setEnabled(True)

        self.status_label.setText(f"✗ 验证出错：{error_message}")
        self.hide_user_info()
        self.show_message("验证出错", f"验证过程中出现错误：{error_message}", QMessageBox.Critical)

    def show_user_info(self, user_info: dict):
        """显示用户信息"""
        # 清空之前的信息
        for i in reversed(range(self.user_info_layout.count())):
            self.user_info_layout.itemAt(i).widget().setParent(None)

        # 添加用户信息
        info_items = [
            ("昵称", user_info.get("nickname", "未知")),
            ("店铺名称", user_info.get("shop_name", "未知")),
            ("店铺ID", user_info.get("platform_shop_id", "未知")),
            ("会员类型", "专业版" if user_info.get("is_pro", False) else "普通版"),
            ("到期时间", user_info.get("deadline", "未知")),
            ("Token到期", user_info.get("token_expire_time", "未知")),
            ("余额充足", "是" if user_info.get("balance_enough", False) else "否"),
            ("闲鱼UP", "已开启" if user_info.get("idle_up", False) else "未开启"),
            ("其他账号", f"{user_info.get('others_count', 0)}个"),
            ("创建时间", user_info.get("create_time", "未知"))
        ]

        row = 0
        for label_text, value_text in info_items:
            label = QLabel(f"{label_text}:")
            label.setStyleSheet("font-weight: bold; color: #495057;")
            value = QLabel(str(value_text))
            value.setStyleSheet("color: #6c757d;")

            self.user_info_layout.addWidget(label, row, 0)
            self.user_info_layout.addWidget(value, row, 1)
            row += 1

        self.user_info_group.setVisible(True)

    def hide_user_info(self):
        """隐藏用户信息"""
        self.user_info_group.setVisible(False)

    def load_saved_config(self):
        """加载已保存的配置"""
        config = self.config_manager.load_config()

        if config.get("authorization") and config.get("cookie"):
            self.update_auth_display(config["authorization"], config["cookie"])
            self.validate_button.setEnabled(True)

            if config.get("validation_status") and config.get("user_info"):
                self.show_user_info(config["user_info"])
                self.status_label.setText("✓ 已加载保存的认证信息")
            else:
                self.status_label.setText("已加载认证信息，建议重新验证")

    def show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """显示消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.exec_()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.api_validator and self.api_validator.isRunning():
            self.api_validator.terminate()
            self.api_validator.wait()
        event.accept()
